"""
Agent Orchestrator for Agentic AI Framework
===========================================

This module provides centralized orchestration of multiple autonomous agents,
managing workflows, coordinating agent interactions, and ensuring system-wide
goal achievement.

Key Features:
- Multi-agent workflow management
- Dynamic agent coordination
- Goal decomposition and distribution
- Resource allocation and conflict resolution
- Performance monitoring and optimization
- Adaptive workflow adjustment
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json

from .base_agent import BaseAgent, AgentState, AgentGoal, AgentCapability
from .agent_communication import AgentCommunicationHub, MessageType, MessagePriority

logger = logging.getLogger(__name__)


class WorkflowState(Enum):
    """Workflow execution states"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(Enum):
    """Types of tasks in workflows"""
    TICKET_INTAKE = "ticket_intake"
    TICKET_CLASSIFICATION = "ticket_classification"
    TECHNICIAN_ASSIGNMENT = "technician_assignment"
    NOTIFICATION_SENDING = "notification_sending"
    MONITORING = "monitoring"
    ESCALATION = "escalation"
    CUSTOM = "custom"


@dataclass
class WorkflowTask:
    """Represents a task within a workflow"""
    task_id: str
    task_type: TaskType
    description: str
    required_capabilities: List[AgentCapability]
    assigned_agent_id: Optional[str] = None
    dependencies: List[str] = field(default_factory=list)  # task_ids this task depends on
    input_data: Dict[str, Any] = field(default_factory=dict)
    output_data: Dict[str, Any] = field(default_factory=dict)
    status: str = "pending"  # pending, assigned, running, completed, failed
    priority: int = 5  # 1-10
    estimated_duration: Optional[int] = None  # seconds
    actual_duration: Optional[int] = None
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


@dataclass
class Workflow:
    """Represents a complete workflow with multiple tasks"""
    workflow_id: str
    name: str
    description: str
    tasks: List[WorkflowTask]
    state: WorkflowState = WorkflowState.PENDING
    context: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    priority: int = 5  # 1-10
    timeout: Optional[int] = None  # seconds
    metadata: Dict[str, Any] = field(default_factory=dict)


class AgentOrchestrator:
    """
    Central orchestrator for managing multiple autonomous agents and workflows.
    
    Responsibilities:
    - Workflow creation and management
    - Task assignment to appropriate agents
    - Agent coordination and communication
    - Resource allocation and conflict resolution
    - Performance monitoring and optimization
    """
    
    def __init__(self, communication_hub: AgentCommunicationHub):
        """
        Initialize the agent orchestrator.
        
        Args:
            communication_hub: Communication hub for agent coordination
        """
        self.communication_hub = communication_hub
        self.agents: Dict[str, BaseAgent] = {}
        self.workflows: Dict[str, Workflow] = {}
        self.active_workflows: List[str] = []
        self.completed_workflows: List[str] = []
        self.failed_workflows: List[str] = []
        
        # Performance tracking
        self.total_workflows = 0
        self.successful_workflows = 0
        self.failed_workflow_count = 0
        self.average_workflow_duration = 0.0
        
        # Resource management
        self.agent_workloads: Dict[str, int] = {}  # agent_id -> current task count
        self.resource_locks: Dict[str, str] = {}  # resource_id -> agent_id
        
        self.running = False
        logger.info("Agent Orchestrator initialized")
    
    def register_agent(self, agent: BaseAgent):
        """Register an agent with the orchestrator"""
        self.agents[agent.agent_id] = agent
        self.agent_workloads[agent.agent_id] = 0
        self.communication_hub.register_agent(agent)
        
        # Subscribe to agent status updates
        self.communication_hub.subscribe(
            agent_id=agent.agent_id,
            message_types=[MessageType.STATUS_UPDATE, MessageType.NOTIFICATION],
            callback=self._handle_agent_message
        )
        
        logger.info(f"Agent {agent.name} registered with orchestrator")
    
    def create_workflow(self, name: str, description: str, tasks: List[Dict[str, Any]], 
                       context: Optional[Dict[str, Any]] = None, priority: int = 5, 
                       timeout: Optional[int] = None) -> str:
        """
        Create a new workflow.
        
        Args:
            name: Workflow name
            description: Workflow description
            tasks: List of task definitions
            context: Workflow context
            priority: Workflow priority (1-10)
            timeout: Workflow timeout in seconds
            
        Returns:
            Workflow ID
        """
        workflow_id = str(uuid.uuid4())
        
        # Convert task definitions to WorkflowTask objects
        workflow_tasks = []
        for task_def in tasks:
            task = WorkflowTask(
                task_id=str(uuid.uuid4()),
                task_type=TaskType(task_def.get('task_type', 'custom')),
                description=task_def.get('description', ''),
                required_capabilities=[AgentCapability(cap) for cap in task_def.get('required_capabilities', [])],
                dependencies=task_def.get('dependencies', []),
                input_data=task_def.get('input_data', {}),
                priority=task_def.get('priority', 5),
                estimated_duration=task_def.get('estimated_duration')
            )
            workflow_tasks.append(task)
        
        workflow = Workflow(
            workflow_id=workflow_id,
            name=name,
            description=description,
            tasks=workflow_tasks,
            context=context or {},
            priority=priority,
            timeout=timeout
        )
        
        self.workflows[workflow_id] = workflow
        self.total_workflows += 1
        
        logger.info(f"Created workflow: {name} ({workflow_id}) with {len(workflow_tasks)} tasks")
        return workflow_id
    
    async def execute_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Execute a workflow by coordinating agents to complete tasks.
        
        Args:
            workflow_id: ID of the workflow to execute
            
        Returns:
            Workflow execution results
        """
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        workflow = self.workflows[workflow_id]
        workflow.state = WorkflowState.RUNNING
        workflow.started_at = datetime.now(timezone.utc)
        self.active_workflows.append(workflow_id)
        
        logger.info(f"Starting workflow execution: {workflow.name} ({workflow_id})")
        
        try:
            # Execute tasks based on dependencies
            completed_tasks = set()
            
            while len(completed_tasks) < len(workflow.tasks):
                # Find tasks that can be executed (dependencies met)
                ready_tasks = [
                    task for task in workflow.tasks
                    if task.status == "pending" and 
                    all(dep_id in completed_tasks for dep_id in task.dependencies)
                ]
                
                if not ready_tasks:
                    # Check if we're stuck (no ready tasks but not all completed)
                    pending_tasks = [task for task in workflow.tasks if task.status == "pending"]
                    if pending_tasks:
                        logger.error(f"Workflow {workflow_id} stuck - no ready tasks but {len(pending_tasks)} pending")
                        workflow.state = WorkflowState.FAILED
                        break
                    else:
                        break
                
                # Execute ready tasks
                task_futures = []
                for task in ready_tasks:
                    future = asyncio.create_task(self._execute_task(workflow, task))
                    task_futures.append(future)
                
                # Wait for tasks to complete
                if task_futures:
                    completed_task_results = await asyncio.gather(*task_futures, return_exceptions=True)
                    
                    for i, result in enumerate(completed_task_results):
                        task = ready_tasks[i]
                        if isinstance(result, Exception):
                            logger.error(f"Task {task.task_id} failed: {result}")
                            task.status = "failed"
                            task.error_message = str(result)
                            workflow.state = WorkflowState.FAILED
                        else:
                            task.status = "completed"
                            task.completed_at = datetime.now(timezone.utc)
                            # Type check to ensure result is Dict[str, Any] as expected
                            if isinstance(result, dict):
                                task.output_data = result
                            else:
                                # Fallback: wrap non-dict results in a dict
                                task.output_data = {"result": result}
                            completed_tasks.add(task.task_id)
                            logger.info(f"Task {task.task_id} completed successfully")
                
                # Check for workflow timeout
                if workflow.timeout:
                    elapsed = (datetime.now(timezone.utc) - workflow.started_at).total_seconds()
                    if elapsed > workflow.timeout:
                        logger.warning(f"Workflow {workflow_id} timed out after {elapsed} seconds")
                        workflow.state = WorkflowState.FAILED
                        break
            
            # Finalize workflow
            workflow.completed_at = datetime.now(timezone.utc)
            
            if workflow.state == WorkflowState.RUNNING:
                workflow.state = WorkflowState.COMPLETED
                self.successful_workflows += 1
                self.completed_workflows.append(workflow_id)
                logger.info(f"Workflow {workflow.name} completed successfully")
            else:
                self.failed_workflow_count += 1
                self.failed_workflows.append(workflow_id)
                logger.error(f"Workflow {workflow.name} failed")
            
            # Remove from active workflows
            if workflow_id in self.active_workflows:
                self.active_workflows.remove(workflow_id)
            
            # Update performance metrics
            self._update_performance_metrics(workflow)
            
            return {
                "workflow_id": workflow_id,
                "state": workflow.state.value,
                "completed_tasks": len(completed_tasks),
                "total_tasks": len(workflow.tasks),
                "duration": (workflow.completed_at - workflow.started_at).total_seconds() if workflow.completed_at else None,
                "results": {task.task_id: task.output_data for task in workflow.tasks if task.status == "completed"}
            }
            
        except Exception as e:
            logger.error(f"Error executing workflow {workflow_id}: {e}")
            workflow.state = WorkflowState.FAILED
            workflow.completed_at = datetime.now(timezone.utc)
            self.failed_workflow_count += 1
            self.failed_workflows.append(workflow_id)
            
            if workflow_id in self.active_workflows:
                self.active_workflows.remove(workflow_id)
            
            raise
    
    async def _execute_task(self, workflow: Workflow, task: WorkflowTask) -> Dict[str, Any]:
        """Execute a single task by assigning it to an appropriate agent"""
        # Find the best agent for this task
        best_agent = self._find_best_agent_for_task(task)
        
        if not best_agent:
            raise Exception(f"No suitable agent found for task {task.task_id}")
        
        # Assign task to agent
        task.assigned_agent_id = best_agent.agent_id
        task.status = "assigned"
        task.started_at = datetime.now(timezone.utc)
        
        # Update agent workload
        self.agent_workloads[best_agent.agent_id] += 1
        
        logger.info(f"Assigned task {task.task_id} to agent {best_agent.name}")
        
        try:
            # Prepare context for the agent
            context = {
                "workflow_id": workflow.workflow_id,
                "task_id": task.task_id,
                "task_type": task.task_type.value,
                "input_data": task.input_data,
                "workflow_context": workflow.context
            }
            
            # Execute the task through the agent
            task.status = "running"
            result = await best_agent.execute_action(
                action={"type": task.task_type.value, "description": task.description},
                context=context
            )
            
            # Calculate actual duration
            if task.started_at:
                task.actual_duration = int((datetime.now(timezone.utc) - task.started_at).total_seconds())
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing task {task.task_id}: {e}")
            raise
        finally:
            # Update agent workload
            self.agent_workloads[best_agent.agent_id] -= 1
    
    def _find_best_agent_for_task(self, task: WorkflowTask) -> Optional[BaseAgent]:
        """Find the best agent to execute a specific task"""
        suitable_agents = []
        
        for agent in self.agents.values():
            # Check if agent has required capabilities
            if all(cap in agent.capabilities for cap in task.required_capabilities):
                # Check agent availability (not overloaded)
                current_workload = self.agent_workloads.get(agent.agent_id, 0)
                if current_workload < 3:  # Max 3 concurrent tasks per agent
                    suitable_agents.append((agent, current_workload))
        
        if not suitable_agents:
            return None
        
        # Sort by workload (prefer less busy agents)
        suitable_agents.sort(key=lambda x: x[1])
        return suitable_agents[0][0]
    
    async def _handle_agent_message(self, message):
        """Handle messages from agents"""
        # Process agent status updates, notifications, etc.
        logger.info(f"Received message from agent {message.sender_id}: {message.message_type.value}")
    
    def _update_performance_metrics(self, workflow: Workflow):
        """Update orchestrator performance metrics"""
        if workflow.started_at and workflow.completed_at:
            duration = (workflow.completed_at - workflow.started_at).total_seconds()
            
            # Update average workflow duration
            total_completed = self.successful_workflows + self.failed_workflow_count
            if total_completed > 0:
                self.average_workflow_duration = (
                    (self.average_workflow_duration * (total_completed - 1) + duration) / total_completed
                )
    
    def get_orchestrator_status(self) -> Dict[str, Any]:
        """Get current orchestrator status and metrics"""
        return {
            "registered_agents": len(self.agents),
            "active_workflows": len(self.active_workflows),
            "total_workflows": self.total_workflows,
            "successful_workflows": self.successful_workflows,
            "failed_workflows": self.failed_workflow_count,
            "success_rate": self.successful_workflows / max(self.total_workflows, 1),
            "average_workflow_duration": self.average_workflow_duration,
            "agent_workloads": self.agent_workloads,
            "running": self.running
        }
    
    async def start(self):
        """Start the orchestrator"""
        self.running = True
        await self.communication_hub.start()
        logger.info("Agent Orchestrator started")
    
    async def stop(self):
        """Stop the orchestrator"""
        self.running = False
        await self.communication_hub.stop()
        logger.info("Agent Orchestrator stopped")
