"""
Assignment Agent AI - Autonomous Technician Assignment
======================================================

This module implements an autonomous AI agent for intelligent technician assignment.
The agent can independently evaluate technicians, make assignment decisions, handle
complex scenarios, and adapt its assignment strategies based on outcomes.

Key Autonomous Capabilities:
- Independent technician evaluation and selection
- Adaptive assignment strategies based on context
- Dynamic workload balancing and optimization
- Intelligent conflict resolution
- Learning from assignment outcomes
- Collaborative decision-making for complex cases
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
import json
import uuid

from .base_agent import BaseAgent, AgentState, AgentCapability, AgentGoal
from .agent_communication import MessageType, MessagePriority

# Import existing components for integration
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

logger = logging.getLogger(__name__)


class AssignmentAgentAI(BaseAgent):
    """
    Autonomous AI Agent for Technician Assignment
    
    This agent exhibits autonomous behavior by:
    - Independently evaluating technician capabilities and availability
    - Making assignment decisions based on multiple criteria
    - Adapting assignment strategies based on outcomes
    - Balancing workloads dynamically
    - Learning from assignment success/failure patterns
    - Collaborating with other agents for complex assignments
    """
    
    def __init__(self, db_connection, google_calendar_credentials_path: str = None, agent_id: str = None):
        """
        Initialize the Assignment Agent AI.
        
        Args:
            db_connection: Database connection for data access
            google_calendar_credentials_path: Path to Google Calendar credentials
            agent_id: Optional custom agent ID
        """
        agent_id = agent_id or f"assignment_agent_{uuid.uuid4().hex[:8]}"
        
        # Define agent capabilities
        capabilities = [
            AgentCapability.ASSIGNMENT,
            AgentCapability.DECISION_MAKING,
            AgentCapability.PLANNING,
            AgentCapability.LEARNING,
            AgentCapability.COMMUNICATION,
            AgentCapability.MONITORING
        ]
        
        super().__init__(agent_id, "Assignment Agent AI", capabilities)
        
        # Core components
        self.db_connection = db_connection
        self.google_calendar_credentials_path = google_calendar_credentials_path
        
        # Assignment strategy parameters
        self.assignment_strategies = {
            "skill_based": {"weight": 0.4, "enabled": True},
            "workload_based": {"weight": 0.3, "enabled": True},
            "availability_based": {"weight": 0.2, "enabled": True},
            "performance_based": {"weight": 0.1, "enabled": True}
        }
        
        # Learning parameters
        self.learning_enabled = True
        self.assignment_confidence_threshold = 0.7
        self.workload_balance_threshold = 0.8
        
        # Performance tracking
        self.assignments_made = 0
        self.successful_assignments = 0
        self.failed_assignments = 0
        self.escalations_triggered = 0
        
        # Technician performance tracking
        self.technician_performance = {}
        self.workload_tracking = {}
        
        # Add initial goals
        self.add_goal(
            description="Assign tickets to optimal technicians",
            priority=10,
            success_criteria=["Assignment accuracy > 85%", "Workload balance maintained"]
        )
        
        self.add_goal(
            description="Optimize assignment strategies continuously",
            priority=8,
            success_criteria=["Learning from outcomes", "Improving assignment quality"]
        )
        
        logger.info(f"Assignment Agent AI {self.agent_id} initialized with autonomous capabilities")
    
    async def think(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Core thinking process for technician assignment analysis.
        
        Args:
            context: Current context including ticket and technician data
            
        Returns:
            Analysis results and potential assignment strategies
        """
        self.state = AgentState.THINKING
        
        ticket_data = context.get('ticket_data', {})
        available_technicians = context.get('available_technicians', [])
        
        # Analyze ticket requirements
        ticket_analysis = await self._analyze_ticket_requirements(ticket_data)
        
        # Evaluate available technicians
        technician_evaluations = await self._evaluate_technicians(
            available_technicians, ticket_analysis
        )
        
        # Analyze current workload distribution
        workload_analysis = await self._analyze_workload_distribution()
        
        # Determine optimal assignment strategy
        assignment_strategy = await self._determine_assignment_strategy(
            ticket_analysis, technician_evaluations, workload_analysis
        )
        
        # Assess assignment confidence
        confidence_assessment = await self._assess_assignment_confidence(
            ticket_analysis, technician_evaluations, assignment_strategy
        )
        
        thought_results = {
            "ticket_analysis": ticket_analysis,
            "technician_evaluations": technician_evaluations,
            "workload_analysis": workload_analysis,
            "assignment_strategy": assignment_strategy,
            "confidence_assessment": confidence_assessment,
            "reasoning": f"Analyzed {len(technician_evaluations)} technicians for "
                        f"{ticket_analysis.get('complexity', 'unknown')} complexity ticket"
        }
        
        logger.info(f"Assignment Agent thinking complete: {thought_results['reasoning']}")
        return thought_results
    
    async def plan(self, goal: AgentGoal, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Create a plan to achieve assignment goals.
        
        Args:
            goal: The goal to plan for
            context: Current context
            
        Returns:
            List of planned actions
        """
        self.state = AgentState.PLANNING
        
        ticket_data = context.get('ticket_data', {})
        thought_results = context.get('thought_results', {})
        
        plan = []
        
        if goal.description == "Assign tickets to optimal technicians":
            # Plan for optimal assignment
            plan = [
                {
                    "action": "fetch_technicians",
                    "description": "Fetch available technicians from database",
                    "priority": 10,
                    "estimated_duration": 3
                },
                {
                    "action": "check_availability",
                    "description": "Check technician availability via calendar",
                    "priority": 9,
                    "estimated_duration": 5
                },
                {
                    "action": "evaluate_skills",
                    "description": "Evaluate technician skills against requirements",
                    "priority": 10,
                    "estimated_duration": 8
                },
                {
                    "action": "calculate_assignments",
                    "description": "Calculate optimal assignment options",
                    "priority": 10,
                    "estimated_duration": 10
                },
                {
                    "action": "make_assignment",
                    "description": "Make final assignment decision",
                    "priority": 10,
                    "estimated_duration": 3
                },
                {
                    "action": "notify_stakeholders",
                    "description": "Notify relevant stakeholders",
                    "priority": 8,
                    "estimated_duration": 5
                }
            ]
            
            # Add collaboration if confidence is low
            confidence = thought_results.get('confidence_assessment', {}).get('confidence', 1.0)
            if confidence < self.assignment_confidence_threshold:
                plan.insert(-2, {
                    "action": "request_collaboration",
                    "description": "Request assistance for complex assignment",
                    "priority": 7,
                    "estimated_duration": 15
                })
        
        elif goal.description == "Optimize assignment strategies continuously":
            # Plan for strategy optimization
            plan = [
                {
                    "action": "analyze_assignment_outcomes",
                    "description": "Analyze recent assignment outcomes",
                    "priority": 6,
                    "estimated_duration": 15
                },
                {
                    "action": "update_strategy_weights",
                    "description": "Update assignment strategy weights",
                    "priority": 7,
                    "estimated_duration": 8
                },
                {
                    "action": "optimize_workload_balance",
                    "description": "Optimize workload distribution",
                    "priority": 6,
                    "estimated_duration": 10
                }
            ]
        
        logger.info(f"Assignment Agent planned {len(plan)} actions for goal: {goal.description}")
        return plan
    
    async def execute_action(self, action: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a specific action autonomously.
        
        Args:
            action: Action to execute
            context: Current context
            
        Returns:
            Execution results
        """
        self.state = AgentState.EXECUTING
        action_type = action.get('type', action.get('action', 'unknown'))
        
        try:
            if action_type == "fetch_technicians":
                return await self._execute_fetch_technicians(context)
            elif action_type == "check_availability":
                return await self._execute_check_availability(context)
            elif action_type == "evaluate_skills":
                return await self._execute_evaluate_skills(context)
            elif action_type == "calculate_assignments":
                return await self._execute_calculate_assignments(context)
            elif action_type == "make_assignment":
                return await self._execute_make_assignment(context)
            elif action_type == "notify_stakeholders":
                return await self._execute_notify_stakeholders(context)
            elif action_type == "request_collaboration":
                return await self._execute_request_collaboration(context)
            elif action_type == "assign_ticket":
                # Main assignment action
                return await self._execute_assign_ticket(context)
            else:
                logger.warning(f"Unknown action type: {action_type}")
                return {"success": False, "error": f"Unknown action: {action_type}"}
                
        except Exception as e:
            logger.error(f"Error executing action {action_type}: {e}")
            return {"success": False, "error": str(e)}

    async def _analyze_ticket_requirements(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze ticket to determine technician requirements"""
        classification = ticket_data.get('classification', {})

        # Extract skill requirements from classification
        issue_type = classification.get('ISSUETYPE', {}).get('Label', 'General')
        sub_issue_type = classification.get('SUBISSUETYPE', {}).get('Label', '')
        priority = classification.get('PRIORITY', {}).get('Label', 'Medium')

        # Determine complexity and required skills
        complexity = self._determine_ticket_complexity(issue_type, sub_issue_type, priority)
        required_skills = self._extract_required_skills(issue_type, sub_issue_type)

        return {
            "issue_type": issue_type,
            "sub_issue_type": sub_issue_type,
            "priority": priority,
            "complexity": complexity,
            "required_skills": required_skills,
            "estimated_effort": self._estimate_effort(complexity, priority)
        }

    async def _evaluate_technicians(self, technicians: List[Dict], ticket_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Evaluate technicians against ticket requirements"""
        evaluations = []
        required_skills = ticket_analysis.get('required_skills', [])

        for tech in technicians:
            evaluation = {
                "technician_id": tech.get('id', ''),
                "name": tech.get('name', ''),
                "email": tech.get('email', ''),
                "skills": tech.get('skills', []),
                "skill_match_score": self._calculate_skill_match(tech.get('skills', []), required_skills),
                "performance_score": self._get_technician_performance(tech.get('id', '')),
                "current_workload": self._get_current_workload(tech.get('id', '')),
                "availability_score": 1.0  # Will be updated by availability check
            }

            # Calculate overall suitability score
            evaluation["suitability_score"] = self._calculate_suitability_score(evaluation)
            evaluations.append(evaluation)

        # Sort by suitability score
        evaluations.sort(key=lambda x: x["suitability_score"], reverse=True)
        return evaluations

    async def _analyze_workload_distribution(self) -> Dict[str, Any]:
        """Analyze current workload distribution across technicians"""
        total_workload = sum(self.workload_tracking.values())
        technician_count = len(self.workload_tracking)

        if technician_count == 0:
            return {"balanced": True, "average_workload": 0, "max_workload": 0}

        average_workload = total_workload / technician_count
        max_workload = max(self.workload_tracking.values()) if self.workload_tracking else 0

        # Check if workload is balanced
        balance_threshold = average_workload * 1.5
        balanced = max_workload <= balance_threshold

        return {
            "balanced": balanced,
            "average_workload": average_workload,
            "max_workload": max_workload,
            "total_workload": total_workload,
            "technician_count": technician_count,
            "workload_distribution": dict(self.workload_tracking)
        }

    async def _determine_assignment_strategy(self, ticket_analysis: Dict[str, Any],
                                           technician_evaluations: List[Dict[str, Any]],
                                           workload_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Determine the optimal assignment strategy"""
        priority = ticket_analysis.get('priority', 'Medium')
        complexity = ticket_analysis.get('complexity', 'medium')
        workload_balanced = workload_analysis.get('balanced', True)

        strategy = {
            "primary_criteria": "skill_match",
            "secondary_criteria": "workload_balance",
            "strategy_weights": dict(self.assignment_strategies)
        }

        # Adjust strategy based on context
        if priority in ['Critical', 'High']:
            strategy["strategy_weights"]["skill_based"]["weight"] = 0.6
            strategy["strategy_weights"]["availability_based"]["weight"] = 0.3

        if not workload_balanced:
            strategy["strategy_weights"]["workload_based"]["weight"] = 0.5
            strategy["primary_criteria"] = "workload_balance"

        if complexity == "high":
            strategy["strategy_weights"]["skill_based"]["weight"] = 0.7
            strategy["strategy_weights"]["performance_based"]["weight"] = 0.2

        return strategy

    async def _assess_assignment_confidence(self, ticket_analysis: Dict[str, Any],
                                          technician_evaluations: List[Dict[str, Any]],
                                          assignment_strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Assess confidence in assignment decision"""
        if not technician_evaluations:
            return {"confidence": 0.0, "needs_escalation": True}

        # Base confidence on best technician's suitability score
        best_score = technician_evaluations[0].get("suitability_score", 0.0)

        # Adjust based on number of suitable candidates
        suitable_candidates = len([t for t in technician_evaluations if t.get("suitability_score", 0) > 0.6])

        confidence = best_score
        if suitable_candidates > 1:
            confidence += 0.1  # More options increase confidence
        elif suitable_candidates == 0:
            confidence = 0.2  # Low confidence if no suitable candidates

        # Adjust based on ticket complexity
        complexity = ticket_analysis.get('complexity', 'medium')
        if complexity == "high" and confidence < 0.8:
            confidence -= 0.2

        confidence = max(0.0, min(1.0, confidence))

        return {
            "confidence": confidence,
            "needs_escalation": confidence < 0.5,
            "needs_collaboration": confidence < self.assignment_confidence_threshold,
            "suitable_candidates": suitable_candidates
        }

    async def _execute_fetch_technicians(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Fetch available technicians from database"""
        try:
            # This would integrate with the existing technician database
            # For now, return mock data structure
            technicians = [
                {
                    "id": "tech_001",
                    "name": "John Smith",
                    "email": "<EMAIL>",
                    "skills": ["Windows", "Network", "Hardware"],
                    "department": "IT Support",
                    "level": "Senior"
                },
                {
                    "id": "tech_002",
                    "name": "Jane Doe",
                    "email": "<EMAIL>",
                    "skills": ["Software", "Database", "Security"],
                    "department": "IT Support",
                    "level": "Expert"
                }
            ]

            return {
                "success": True,
                "technicians": technicians,
                "count": len(technicians)
            }
        except Exception as e:
            logger.error(f"Failed to fetch technicians: {e}")
            return {
                "success": False,
                "error": str(e),
                "technicians": []
            }

    async def _execute_check_availability(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Check technician availability via calendar integration"""
        technicians = context.get('technicians', [])

        try:
            available_technicians = []

            for tech in technicians:
                # Check calendar availability (would integrate with Google Calendar)
                is_available = await self._check_technician_calendar(tech.get('email', ''))

                if is_available:
                    tech['availability_score'] = 1.0
                    available_technicians.append(tech)
                else:
                    tech['availability_score'] = 0.3  # Partially available
                    available_technicians.append(tech)  # Still include but with lower score

            return {
                "success": True,
                "available_technicians": available_technicians,
                "fully_available": len([t for t in available_technicians if t.get('availability_score', 0) == 1.0])
            }
        except Exception as e:
            logger.error(f"Availability check failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "available_technicians": technicians  # Fallback to all technicians
            }

    async def _check_technician_calendar(self, email: str) -> bool:
        """Check if technician is available via calendar"""
        # This would integrate with Google Calendar API
        # For now, return True (available) as default
        return True

    def _determine_ticket_complexity(self, issue_type: str, sub_issue_type: str, priority: str) -> str:
        """Determine ticket complexity based on classification"""
        complexity_score = 0

        # Priority contribution
        if priority in ['Critical', 'High']:
            complexity_score += 2
        elif priority == 'Medium':
            complexity_score += 1

        # Issue type contribution
        complex_issues = ['Server', 'Database', 'Security', 'Network Infrastructure']
        if any(complex_issue in issue_type for complex_issue in complex_issues):
            complexity_score += 2

        # Sub-issue type contribution
        if sub_issue_type and len(sub_issue_type) > 20:  # Detailed sub-issues are often complex
            complexity_score += 1

        if complexity_score >= 4:
            return "high"
        elif complexity_score >= 2:
            return "medium"
        else:
            return "low"

    def _extract_required_skills(self, issue_type: str, sub_issue_type: str) -> List[str]:
        """Extract required skills from issue classification"""
        skills = []

        # Map issue types to skills
        skill_mapping = {
            "Hardware": ["Hardware", "Troubleshooting"],
            "Software": ["Software", "Application Support"],
            "Network": ["Network", "Connectivity"],
            "Security": ["Security", "Cybersecurity"],
            "Database": ["Database", "SQL"],
            "Server": ["Server", "System Administration"]
        }

        for issue, skill_list in skill_mapping.items():
            if issue.lower() in issue_type.lower():
                skills.extend(skill_list)

        return list(set(skills))  # Remove duplicates

    def _calculate_skill_match(self, technician_skills: List[str], required_skills: List[str]) -> float:
        """Calculate skill match score between technician and requirements"""
        if not required_skills:
            return 1.0  # No specific requirements

        if not technician_skills:
            return 0.0  # No skills listed

        # Convert to lowercase for comparison
        tech_skills_lower = [skill.lower() for skill in technician_skills]
        required_skills_lower = [skill.lower() for skill in required_skills]

        # Calculate match percentage
        matches = sum(1 for skill in required_skills_lower if skill in tech_skills_lower)
        return matches / len(required_skills)

    def _get_technician_performance(self, technician_id: str) -> float:
        """Get technician performance score from historical data"""
        if technician_id in self.technician_performance:
            return self.technician_performance[technician_id]
        else:
            return 0.8  # Default performance score for new technicians

    def _get_current_workload(self, technician_id: str) -> int:
        """Get current workload for technician"""
        return self.workload_tracking.get(technician_id, 0)

    def _calculate_suitability_score(self, evaluation: Dict[str, Any]) -> float:
        """Calculate overall suitability score for technician"""
        weights = self.assignment_strategies

        skill_score = evaluation.get('skill_match_score', 0.0)
        performance_score = evaluation.get('performance_score', 0.0)
        availability_score = evaluation.get('availability_score', 0.0)

        # Workload score (inverse of workload - lower workload is better)
        workload = evaluation.get('current_workload', 0)
        workload_score = max(0.0, 1.0 - (workload / 10.0))  # Normalize to 0-1

        # Calculate weighted score
        total_score = (
            skill_score * weights["skill_based"]["weight"] +
            workload_score * weights["workload_based"]["weight"] +
            availability_score * weights["availability_based"]["weight"] +
            performance_score * weights["performance_based"]["weight"]
        )

        return min(1.0, total_score)

    def _estimate_effort(self, complexity: str, priority: str) -> int:
        """Estimate effort required for ticket (in hours)"""
        base_effort = {
            "low": 2,
            "medium": 4,
            "high": 8
        }

        effort = base_effort.get(complexity, 4)

        # Adjust for priority
        if priority in ['Critical', 'High']:
            effort = int(effort * 1.5)  # High priority may require more thorough work

        return effort

    async def _execute_evaluate_skills(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate technician skills against ticket requirements"""
        ticket_data = context.get('ticket_data', {})
        technicians = context.get('technicians', [])

        try:
            # Analyze ticket requirements
            ticket_analysis = await self._analyze_ticket_requirements(ticket_data)

            # Evaluate each technician
            evaluated_technicians = await self._evaluate_technicians(technicians, ticket_analysis)

            return {
                "success": True,
                "evaluated_technicians": evaluated_technicians,
                "ticket_requirements": ticket_analysis
            }
        except Exception as e:
            logger.error(f"Skill evaluation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "evaluated_technicians": []
            }

    async def _execute_calculate_assignments(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate optimal assignment options"""
        ticket_data = context.get('ticket_data', {})
        evaluated_technicians = context.get('evaluated_technicians', [])

        try:
            assignment_options = []

            # Create assignment options from top candidates
            for i, tech in enumerate(evaluated_technicians[:3]):  # Top 3 candidates
                option = {
                    "rank": i + 1,
                    "technician": tech,
                    "assignment_score": tech.get('suitability_score', 0.0),
                    "reasoning": self._generate_assignment_reasoning(tech, ticket_data),
                    "estimated_completion": self._estimate_completion_time(tech, ticket_data)
                }
                assignment_options.append(option)

            return {
                "success": True,
                "assignment_options": assignment_options,
                "recommended_option": assignment_options[0] if assignment_options else None
            }
        except Exception as e:
            logger.error(f"Assignment calculation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "assignment_options": []
            }

    async def _execute_make_assignment(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Make the final assignment decision"""
        assignment_options = context.get('assignment_options', [])
        ticket_data = context.get('ticket_data', {})

        if not assignment_options:
            return {
                "success": False,
                "error": "No assignment options available",
                "assignment": {}
            }

        try:
            # Select the best option (first in ranked list)
            selected_option = assignment_options[0]
            technician = selected_option["technician"]

            # Create assignment record
            assignment = {
                "ticket_id": ticket_data.get('ticket_number', ''),
                "technician_id": technician.get('technician_id', ''),
                "technician_name": technician.get('name', ''),
                "technician_email": technician.get('email', ''),
                "assignment_score": selected_option.get('assignment_score', 0.0),
                "assignment_reasoning": selected_option.get('reasoning', ''),
                "estimated_completion": selected_option.get('estimated_completion', ''),
                "assigned_at": datetime.now(timezone.utc).isoformat(),
                "assigned_by": self.agent_id
            }

            # Update workload tracking
            tech_id = technician.get('technician_id', '')
            if tech_id:
                self.workload_tracking[tech_id] = self.workload_tracking.get(tech_id, 0) + 1

            return {
                "success": True,
                "assignment": assignment,
                "selected_option_rank": selected_option.get('rank', 1)
            }
        except Exception as e:
            logger.error(f"Assignment decision failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "assignment": {}
            }

    async def _execute_notify_stakeholders(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Notify stakeholders about the assignment"""
        assignment = context.get('assignment', {})
        ticket_data = context.get('ticket_data', {})

        if not self.communication_hub:
            return {"success": False, "error": "No communication hub available"}

        try:
            # Notify notification agent to send emails
            await self.communicate(
                recipient_id="notification_agent",
                message_type="request",
                content={
                    "action": "send_assignment_notifications",
                    "assignment": assignment,
                    "ticket_data": ticket_data
                }
            )

            return {
                "success": True,
                "notifications_triggered": True,
                "notified_agents": ["notification_agent"]
            }
        except Exception as e:
            logger.error(f"Stakeholder notification failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_request_collaboration(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Request collaboration for complex assignments"""
        if not self.communication_hub:
            return {"success": False, "error": "No communication hub available"}

        try:
            await self.communicate(
                recipient_id="ALL",
                message_type="collaboration",
                content={
                    "request_type": "assignment_assistance",
                    "ticket_data": context.get('ticket_data', {}),
                    "current_analysis": context.get('thought_results', {}),
                    "confidence_level": context.get('confidence', 0.0)
                }
            )

            return {
                "success": True,
                "collaboration_requested": True,
                "message": "Collaboration request sent to all agents"
            }
        except Exception as e:
            logger.error(f"Collaboration request failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _generate_assignment_reasoning(self, technician: Dict[str, Any], ticket_data: Dict[str, Any]) -> str:
        """Generate reasoning for assignment decision"""
        skill_score = technician.get('skill_match_score', 0.0)
        workload = technician.get('current_workload', 0)
        availability = technician.get('availability_score', 0.0)

        reasoning = f"Selected {technician.get('name', 'Unknown')} based on: "
        reasoning += f"skill match ({skill_score:.1%}), "
        reasoning += f"current workload ({workload} tickets), "
        reasoning += f"availability ({availability:.1%})"

        return reasoning

    def _estimate_completion_time(self, technician: Dict[str, Any], ticket_data: Dict[str, Any]) -> str:
        """Estimate completion time for assignment"""
        # Base estimation on complexity and technician workload
        complexity = ticket_data.get('classification', {}).get('complexity', 'medium')
        workload = technician.get('current_workload', 0)

        base_hours = {"low": 4, "medium": 8, "high": 16}
        estimated_hours = base_hours.get(complexity, 8)

        # Adjust for workload
        estimated_hours += workload * 2  # Each existing ticket adds 2 hours delay

        completion_time = datetime.now(timezone.utc) + timedelta(hours=estimated_hours)
        return completion_time.isoformat()

    async def assign_ticket_autonomous(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main autonomous ticket assignment method.
        This is the entry point for assigning tickets autonomously.
        """
        # Add assignment goal
        goal_id = self.add_goal(
            description=f"Assign ticket: {ticket_data.get('ticket_number', 'Unknown')}",
            priority=10,
            context={"ticket_data": ticket_data}
        )

        try:
            # Execute the main assignment action
            result = await self.execute_action(
                action={"type": "assign_ticket"},
                context={"ticket_data": ticket_data}
            )

            # Learn from the outcome
            await self.learn_from_outcome(
                decision=None,  # Would be actual decision object
                outcome=result
            )

            # Complete the goal
            self.complete_goal(goal_id, "completed" if result.get("success") else "failed")

            return result

        except Exception as e:
            logger.error(f"Autonomous ticket assignment failed: {e}")
            self.complete_goal(goal_id, "failed")
            return {"success": False, "error": str(e)}

    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific performance metrics"""
        base_metrics = self.get_status()

        # Add assignment-specific metrics
        assignment_metrics = {
            "assignments_made": self.assignments_made,
            "successful_assignments": self.successful_assignments,
            "failed_assignments": self.failed_assignments,
            "assignment_success_rate": (
                self.successful_assignments / max(self.assignments_made, 1)
            ),
            "escalations_triggered": self.escalations_triggered,
            "average_confidence": self.assignment_confidence_threshold,
            "current_workload_distribution": dict(self.workload_tracking),
            "learning_enabled": self.learning_enabled
        }

        return {**base_metrics, **assignment_metrics}
    
    async def _execute_assign_ticket(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the main ticket assignment workflow"""
        ticket_data = context.get('input_data', {}).get('ticket_data', context.get('ticket_data', {}))
        
        if not ticket_data:
            return {"success": False, "error": "No ticket data provided"}
        
        try:
            # Step 1: Think about the assignment
            thought_results = await self.think({"ticket_data": ticket_data})
            
            # Step 2: Fetch available technicians
            technicians_result = await self._execute_fetch_technicians({"ticket_data": ticket_data})
            
            # Step 3: Check availability
            availability_result = await self._execute_check_availability({
                "technicians": technicians_result.get("technicians", [])
            })
            
            # Step 4: Evaluate skills
            skills_result = await self._execute_evaluate_skills({
                "ticket_data": ticket_data,
                "technicians": availability_result.get("available_technicians", [])
            })
            
            # Step 5: Calculate optimal assignments
            assignment_result = await self._execute_calculate_assignments({
                "ticket_data": ticket_data,
                "evaluated_technicians": skills_result.get("evaluated_technicians", [])
            })
            
            # Step 6: Make final assignment
            final_assignment = await self._execute_make_assignment({
                "assignment_options": assignment_result.get("assignment_options", []),
                "ticket_data": ticket_data
            })
            
            # Step 7: Notify stakeholders
            notification_result = await self._execute_notify_stakeholders({
                "assignment": final_assignment.get("assignment", {}),
                "ticket_data": ticket_data
            })
            
            # Update performance metrics
            self.assignments_made += 1
            if final_assignment.get("success", False):
                self.successful_assignments += 1
            else:
                self.failed_assignments += 1
            
            # Prepare final result
            result = {
                "success": final_assignment.get("success", False),
                "assignment": final_assignment.get("assignment", {}),
                "reasoning": thought_results.get("reasoning", ""),
                "confidence": thought_results.get("confidence_assessment", {}).get("confidence", 0.0),
                "assignment_time": datetime.now(timezone.utc).isoformat(),
                "notification_sent": notification_result.get("success", False)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error in ticket assignment: {e}")
            self.failed_assignments += 1
            return {"success": False, "error": str(e)}
