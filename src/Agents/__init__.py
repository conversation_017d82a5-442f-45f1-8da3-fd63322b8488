"""
Agentic AI Framework for TeamLogic-AutoTask
============================================

This package contains autonomous AI agents that can act independently, make decisions,
and execute tasks with minimal human intervention. Each agent exhibits agency - the 
capacity to take initiative, plan actions, and achieve specific goals.

Core Components:
- BaseAgent: Foundation class for all autonomous agents
- AgentOrchestrator: Manages multi-agent workflows and coordination
- AgentCommunication: Handles inter-agent communication and collaboration
- Autonomous Agents: Specialized agents for different domains

Author: AutoTask Agentic AI System
Date: 2025-07-29
"""

from .base_agent import BaseAgent, AgentState, AgentCapability
from .agent_orchestrator import AgentOrchestrator
from .agent_communication import AgentCommunicationHub, AgentMessage
from .intake_agent_ai import IntakeAgentAI
from .assignment_agent_ai import AssignmentAgentAI
from .notification_agent_ai import NotificationAgentAI
from .monitoring_agent_ai import MonitoringAgentAI
from .escalation_agent_ai import EscalationAgentAI
from .integration_bridge import AgenticAIBridge

__all__ = [
    'BaseAgent',
    'AgentState', 
    'AgentCapability',
    'AgentOrchestrator',
    'AgentCommunicationHub',
    'AgentMessage',
    'IntakeAgentAI',
    'AssignmentAgentAI',
    'NotificationAgentAI',
    'MonitoringAgentAI',
    'EscalationAgentAI',
    'AgenticAIBridge'
]

__version__ = "1.0.0"
__author__ = "AutoTask Agentic AI System"
