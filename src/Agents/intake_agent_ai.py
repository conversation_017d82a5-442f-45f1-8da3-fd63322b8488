"""
Intake Agent AI - Autonomous Ticket Intake and Classification
============================================================

This module implements an autonomous AI agent for intelligent ticket intake and classification.
The agent can independently analyze incoming tickets, make classification decisions, extract
metadata, and trigger appropriate workflows without explicit scripting.

Key Autonomous Capabilities:
- Independent ticket analysis and understanding
- Adaptive classification based on context and patterns
- Intelligent metadata extraction
- Dynamic workflow triggering
- Learning from classification outcomes
- Collaborative decision-making with other agents
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import json
import uuid

from .base_agent import BaseAgent, AgentState, AgentCapability, AgentGoal
from .agent_communication import MessageType, MessagePriority

# Import existing components for integration
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.processors import AIProcessor, TicketProcessor
from src.data import DataManager

logger = logging.getLogger(__name__)


class IntakeAgentAI(BaseAgent):
    """
    Autonomous AI Agent for Ticket Intake and Classification
    
    This agent exhibits autonomous behavior by:
    - Independently analyzing ticket content and context
    - Making classification decisions based on learned patterns
    - Adapting classification strategies based on outcomes
    - Collaborating with other agents for complex cases
    - Continuously learning and improving performance
    """
    
    def __init__(self, db_connection, data_manager: DataManager, agent_id: str = None):
        """
        Initialize the Intake Agent AI.
        
        Args:
            db_connection: Database connection for data access
            data_manager: Data manager for reference data
            agent_id: Optional custom agent ID
        """
        agent_id = agent_id or f"intake_agent_{uuid.uuid4().hex[:8]}"
        
        # Define agent capabilities
        capabilities = [
            AgentCapability.TICKET_ANALYSIS,
            AgentCapability.CLASSIFICATION,
            AgentCapability.LEARNING,
            AgentCapability.COMMUNICATION,
            AgentCapability.DECISION_MAKING,
            AgentCapability.PLANNING
        ]
        
        super().__init__(agent_id, "Intake Agent AI", capabilities)
        
        # Core components
        self.db_connection = db_connection
        self.data_manager = data_manager
        self.ai_processor = AIProcessor(db_connection, data_manager.reference_data)
        self.ticket_processor = TicketProcessor(data_manager.reference_data)
        
        # Agent-specific attributes
        self.classification_confidence_threshold = 0.8
        self.learning_enabled = True
        self.collaboration_threshold = 0.6  # When to ask for help
        
        # Performance tracking
        self.tickets_processed = 0
        self.successful_classifications = 0
        self.collaboration_requests = 0
        
        # Add initial goals
        self.add_goal(
            description="Process incoming tickets with high accuracy",
            priority=9,
            success_criteria=["Classification accuracy > 90%", "Processing time < 30 seconds"]
        )
        
        self.add_goal(
            description="Continuously improve classification performance",
            priority=7,
            success_criteria=["Learning from each ticket", "Adapting strategies based on outcomes"]
        )
        
        logger.info(f"Intake Agent AI {self.agent_id} initialized with autonomous capabilities")
    
    async def think(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Core thinking process for ticket analysis and classification.
        
        Args:
            context: Current context including ticket data
            
        Returns:
            Analysis results and potential actions
        """
        self.state = AgentState.THINKING
        
        ticket_data = context.get('ticket_data', {})
        
        # Analyze ticket complexity and requirements
        complexity_analysis = await self._analyze_ticket_complexity(ticket_data)
        
        # Determine classification strategy
        strategy = await self._determine_classification_strategy(ticket_data, complexity_analysis)
        
        # Assess confidence and need for collaboration
        confidence_assessment = await self._assess_classification_confidence(ticket_data, strategy)
        
        # Generate potential actions
        potential_actions = await self._generate_potential_actions(
            ticket_data, complexity_analysis, strategy, confidence_assessment
        )
        
        thought_results = {
            "complexity_analysis": complexity_analysis,
            "classification_strategy": strategy,
            "confidence_assessment": confidence_assessment,
            "potential_actions": potential_actions,
            "reasoning": f"Analyzed ticket with {strategy['approach']} strategy, "
                        f"confidence: {confidence_assessment['confidence']:.2f}"
        }
        
        logger.info(f"Intake Agent thinking complete: {thought_results['reasoning']}")
        return thought_results
    
    async def plan(self, goal: AgentGoal, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Create a plan to achieve ticket processing goals.
        
        Args:
            goal: The goal to plan for
            context: Current context
            
        Returns:
            List of planned actions
        """
        self.state = AgentState.PLANNING
        
        ticket_data = context.get('ticket_data', {})
        thought_results = context.get('thought_results', {})
        
        plan = []
        
        if goal.description == "Process incoming tickets with high accuracy":
            # Plan for accurate ticket processing
            plan = [
                {
                    "action": "extract_metadata",
                    "description": "Extract comprehensive metadata from ticket",
                    "priority": 10,
                    "estimated_duration": 5
                },
                {
                    "action": "find_similar_tickets",
                    "description": "Find similar historical tickets for context",
                    "priority": 9,
                    "estimated_duration": 8
                },
                {
                    "action": "classify_ticket",
                    "description": "Classify ticket using AI analysis",
                    "priority": 10,
                    "estimated_duration": 10
                },
                {
                    "action": "validate_classification",
                    "description": "Validate classification results",
                    "priority": 8,
                    "estimated_duration": 3
                },
                {
                    "action": "trigger_next_workflow",
                    "description": "Trigger assignment workflow",
                    "priority": 7,
                    "estimated_duration": 2
                }
            ]
            
            # Add collaboration if confidence is low
            confidence = thought_results.get('confidence_assessment', {}).get('confidence', 1.0)
            if confidence < self.collaboration_threshold:
                plan.insert(-1, {
                    "action": "request_collaboration",
                    "description": "Request assistance from other agents",
                    "priority": 6,
                    "estimated_duration": 15
                })
        
        elif goal.description == "Continuously improve classification performance":
            # Plan for learning and improvement
            plan = [
                {
                    "action": "analyze_recent_performance",
                    "description": "Analyze recent classification performance",
                    "priority": 5,
                    "estimated_duration": 10
                },
                {
                    "action": "identify_improvement_areas",
                    "description": "Identify areas for improvement",
                    "priority": 6,
                    "estimated_duration": 8
                },
                {
                    "action": "update_strategies",
                    "description": "Update classification strategies",
                    "priority": 7,
                    "estimated_duration": 5
                }
            ]
        
        logger.info(f"Intake Agent planned {len(plan)} actions for goal: {goal.description}")
        return plan
    
    async def execute_action(self, action: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a specific action autonomously.
        
        Args:
            action: Action to execute
            context: Current context
            
        Returns:
            Execution results
        """
        self.state = AgentState.EXECUTING
        action_type = action.get('type', action.get('action', 'unknown'))
        
        try:
            if action_type == "extract_metadata":
                return await self._execute_extract_metadata(context)
            elif action_type == "find_similar_tickets":
                return await self._execute_find_similar_tickets(context)
            elif action_type == "classify_ticket":
                return await self._execute_classify_ticket(context)
            elif action_type == "validate_classification":
                return await self._execute_validate_classification(context)
            elif action_type == "request_collaboration":
                return await self._execute_request_collaboration(context)
            elif action_type == "trigger_next_workflow":
                return await self._execute_trigger_next_workflow(context)
            elif action_type == "process_ticket":
                # Main ticket processing action
                return await self._execute_process_ticket(context)
            else:
                logger.warning(f"Unknown action type: {action_type}")
                return {"success": False, "error": f"Unknown action: {action_type}"}
                
        except Exception as e:
            logger.error(f"Error executing action {action_type}: {e}")
            return {"success": False, "error": str(e)}
        finally:
            self.state = AgentState.IDLE
    
    async def _execute_process_ticket(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the main ticket processing workflow"""
        ticket_data = context.get('input_data', {}).get('ticket_data', context.get('ticket_data', {}))
        
        if not ticket_data:
            return {"success": False, "error": "No ticket data provided"}
        
        try:
            # Step 1: Think about the ticket
            thought_results = await self.think({"ticket_data": ticket_data})
            
            # Step 2: Extract metadata
            metadata_result = await self._execute_extract_metadata({"ticket_data": ticket_data})
            
            # Step 3: Find similar tickets
            similar_tickets_result = await self._execute_find_similar_tickets({
                "ticket_data": ticket_data,
                "extracted_metadata": metadata_result.get("metadata", {})
            })
            
            # Step 4: Classify ticket
            classification_result = await self._execute_classify_ticket({
                "ticket_data": ticket_data,
                "extracted_metadata": metadata_result.get("metadata", {}),
                "similar_tickets": similar_tickets_result.get("similar_tickets", [])
            })
            
            # Step 5: Validate and finalize
            validation_result = await self._execute_validate_classification({
                "classification": classification_result.get("classification", {}),
                "confidence": classification_result.get("confidence", 0.0)
            })
            
            # Update performance metrics
            self.tickets_processed += 1
            if validation_result.get("valid", False):
                self.successful_classifications += 1
            
            # Prepare final result
            result = {
                "success": True,
                "ticket_data": ticket_data,
                "extracted_metadata": metadata_result.get("metadata", {}),
                "similar_tickets": similar_tickets_result.get("similar_tickets", []),
                "classification": classification_result.get("classification", {}),
                "confidence": classification_result.get("confidence", 0.0),
                "validation": validation_result,
                "agent_reasoning": thought_results.get("reasoning", ""),
                "processing_time": datetime.now(timezone.utc).isoformat()
            }
            
            # Trigger next workflow if classification is successful
            if validation_result.get("valid", False):
                await self._execute_trigger_next_workflow({"processed_ticket": result})
            
            return result
            
        except Exception as e:
            logger.error(f"Error in ticket processing: {e}")
            return {"success": False, "error": str(e)}
    
    async def _analyze_ticket_complexity(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the complexity of a ticket"""
        title = ticket_data.get('title', '')
        description = ticket_data.get('description', '')
        
        # Analyze text complexity
        text_length = len(title) + len(description)
        word_count = len((title + ' ' + description).split())
        
        # Check for technical keywords
        technical_keywords = self.ticket_processor.extract_technical_keywords(title, description)
        
        # Determine complexity level
        complexity_score = 0
        if text_length > 500:
            complexity_score += 2
        if word_count > 100:
            complexity_score += 2
        if len(technical_keywords) > 3:
            complexity_score += 3
        
        complexity_level = "low" if complexity_score < 3 else "medium" if complexity_score < 6 else "high"
        
        return {
            "complexity_level": complexity_level,
            "complexity_score": complexity_score,
            "text_length": text_length,
            "word_count": word_count,
            "technical_keywords": technical_keywords
        }
    
    async def _determine_classification_strategy(self, ticket_data: Dict[str, Any], 
                                               complexity_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Determine the best classification strategy based on ticket analysis"""
        complexity_level = complexity_analysis.get('complexity_level', 'medium')
        
        if complexity_level == "low":
            strategy = {
                "approach": "fast_classification",
                "use_similarity": True,
                "similarity_threshold": 0.8,
                "llm_model": "llama3-8b",
                "confidence_threshold": 0.7
            }
        elif complexity_level == "medium":
            strategy = {
                "approach": "balanced_classification",
                "use_similarity": True,
                "similarity_threshold": 0.7,
                "llm_model": "mixtral-8x7b",
                "confidence_threshold": 0.8
            }
        else:  # high complexity
            strategy = {
                "approach": "thorough_classification",
                "use_similarity": True,
                "similarity_threshold": 0.6,
                "llm_model": "mixtral-8x7b",
                "confidence_threshold": 0.9,
                "require_validation": True
            }
        
        return strategy
    
    async def _assess_classification_confidence(self, ticket_data: Dict[str, Any], 
                                              strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Assess confidence in classification ability"""
        # Base confidence on strategy and past performance
        base_confidence = 0.8
        
        # Adjust based on performance history
        if self.tickets_processed > 0:
            success_rate = self.successful_classifications / self.tickets_processed
            base_confidence = (base_confidence + success_rate) / 2
        
        # Adjust based on strategy
        if strategy['approach'] == "thorough_classification":
            base_confidence += 0.1
        elif strategy['approach'] == "fast_classification":
            base_confidence -= 0.1
        
        # Ensure confidence is within bounds
        confidence = max(0.0, min(1.0, base_confidence))
        
        return {
            "confidence": confidence,
            "needs_collaboration": confidence < self.collaboration_threshold,
            "strategy_adjustment": strategy['approach']
        }
    
    async def _generate_potential_actions(self, ticket_data: Dict[str, Any], 
                                        complexity_analysis: Dict[str, Any],
                                        strategy: Dict[str, Any], 
                                        confidence_assessment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate potential actions based on analysis"""
        actions = [
            {"action": "extract_metadata", "priority": 10},
            {"action": "find_similar_tickets", "priority": 9},
            {"action": "classify_ticket", "priority": 10}
        ]
        
        if confidence_assessment.get('needs_collaboration', False):
            actions.append({"action": "request_collaboration", "priority": 8})
        
        if strategy.get('require_validation', False):
            actions.append({"action": "validate_classification", "priority": 7})
        
        actions.append({"action": "trigger_next_workflow", "priority": 6})
        
        return actions

    async def _execute_extract_metadata(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract metadata from ticket using AI processor"""
        ticket_data = context.get('ticket_data', {})

        try:
            # Use existing AI processor for metadata extraction
            extracted_metadata = self.ai_processor.extract_metadata(
                ticket_data.get('title', ''),
                ticket_data.get('description', ''),
                model='llama3-8b'
            )

            return {
                "success": True,
                "metadata": extracted_metadata or {},
                "extraction_method": "ai_processor"
            }
        except Exception as e:
            logger.error(f"Metadata extraction failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "metadata": {}
            }

    async def _execute_find_similar_tickets(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Find similar tickets using semantic similarity"""
        ticket_data = context.get('ticket_data', {})

        try:
            # Create ticket text for similarity search
            ticket_text = f"{ticket_data.get('title', '')} {ticket_data.get('description', '')}"

            # Use semantic similarity search (from original agent)
            similar_tickets = []
            if hasattr(self, '_find_similar_tickets_semantic'):
                similar_tickets = self._find_similar_tickets_semantic(ticket_text, top_n=5)

            return {
                "success": True,
                "similar_tickets": similar_tickets,
                "search_method": "semantic_similarity"
            }
        except Exception as e:
            logger.error(f"Similar tickets search failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "similar_tickets": []
            }

    async def _execute_classify_ticket(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Classify ticket using AI processor"""
        ticket_data = context.get('ticket_data', {})
        extracted_metadata = context.get('extracted_metadata', {})
        similar_tickets = context.get('similar_tickets', [])

        try:
            # Use existing AI processor for classification
            classification = self.ai_processor.classify_ticket(
                ticket_data,
                extracted_metadata,
                similar_tickets,
                model='mixtral-8x7b'
            )

            # Calculate confidence based on classification quality
            confidence = self._calculate_classification_confidence(classification)

            return {
                "success": True,
                "classification": classification or {},
                "confidence": confidence,
                "classification_method": "ai_processor"
            }
        except Exception as e:
            logger.error(f"Ticket classification failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "classification": {},
                "confidence": 0.0
            }

    async def _execute_validate_classification(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Validate classification results"""
        classification = context.get('classification', {})
        confidence = context.get('confidence', 0.0)

        # Validation criteria
        required_fields = ['ISSUETYPE', 'SUBISSUETYPE', 'TICKETCATEGORY', 'PRIORITY']

        valid = True
        validation_issues = []

        # Check if all required fields are present
        for field in required_fields:
            if field not in classification or not classification[field]:
                valid = False
                validation_issues.append(f"Missing {field}")

        # Check confidence threshold
        if confidence < self.classification_confidence_threshold:
            valid = False
            validation_issues.append(f"Low confidence: {confidence:.2f}")

        return {
            "valid": valid,
            "confidence": confidence,
            "validation_issues": validation_issues,
            "classification_complete": len(validation_issues) == 0
        }

    async def _execute_request_collaboration(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Request collaboration from other agents"""
        if not self.communication_hub:
            return {"success": False, "error": "No communication hub available"}

        try:
            # Send collaboration request
            await self.communicate(
                recipient_id="ALL",
                message_type="collaboration",
                content={
                    "request_type": "classification_assistance",
                    "ticket_data": context.get('ticket_data', {}),
                    "current_analysis": context.get('thought_results', {}),
                    "confidence_level": context.get('confidence', 0.0)
                }
            )

            self.collaboration_requests += 1

            return {
                "success": True,
                "collaboration_requested": True,
                "message": "Collaboration request sent to all agents"
            }
        except Exception as e:
            logger.error(f"Collaboration request failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_trigger_next_workflow(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Trigger the next workflow (assignment)"""
        processed_ticket = context.get('processed_ticket', {})

        if not self.communication_hub:
            return {"success": False, "error": "No communication hub available"}

        try:
            # Send message to assignment agent
            await self.communicate(
                recipient_id="assignment_agent",
                message_type="request",
                content={
                    "action": "assign_ticket",
                    "ticket_data": processed_ticket,
                    "priority": "normal"
                }
            )

            return {
                "success": True,
                "next_workflow_triggered": True,
                "target_agent": "assignment_agent"
            }
        except Exception as e:
            logger.error(f"Failed to trigger next workflow: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _calculate_classification_confidence(self, classification: Dict[str, Any]) -> float:
        """Calculate confidence score for classification"""
        if not classification:
            return 0.0

        # Base confidence
        confidence = 0.5

        # Check completeness
        required_fields = ['ISSUETYPE', 'SUBISSUETYPE', 'TICKETCATEGORY', 'PRIORITY']
        completed_fields = sum(1 for field in required_fields if field in classification and classification[field])
        completeness_score = completed_fields / len(required_fields)

        # Adjust confidence based on completeness
        confidence += completeness_score * 0.4

        # Check for detailed labels (indicates good classification)
        for field in required_fields:
            if field in classification and isinstance(classification[field], dict):
                if 'Label' in classification[field] and classification[field]['Label']:
                    confidence += 0.025

        return min(1.0, confidence)

    # Integration methods for backward compatibility
    def _find_similar_tickets_semantic(self, ticket_text: str, top_n: int = 5) -> List[Dict]:
        """Find similar tickets using semantic similarity (integration with existing system)"""
        try:
            # This would integrate with the existing semantic similarity search
            # For now, return empty list - will be implemented when integrating with existing system
            return []
        except Exception as e:
            logger.error(f"Semantic similarity search failed: {e}")
            return []

    async def process_ticket_autonomous(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main autonomous ticket processing method.
        This is the entry point for processing tickets autonomously.
        """
        # Add processing goal
        goal_id = self.add_goal(
            description=f"Process ticket: {ticket_data.get('title', 'Unknown')}",
            priority=10,
            context={"ticket_data": ticket_data}
        )

        try:
            # Execute the main processing action
            result = await self.execute_action(
                action={"type": "process_ticket"},
                context={"ticket_data": ticket_data}
            )

            # Learn from the outcome
            await self.learn_from_outcome(
                decision=None,  # Would be actual decision object
                outcome=result
            )

            # Complete the goal
            self.complete_goal(goal_id, "completed" if result.get("success") else "failed")

            return result

        except Exception as e:
            logger.error(f"Autonomous ticket processing failed: {e}")
            self.complete_goal(goal_id, "failed")
            return {"success": False, "error": str(e)}

    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific performance metrics"""
        base_metrics = self.get_status()

        # Add intake-specific metrics
        intake_metrics = {
            "tickets_processed": self.tickets_processed,
            "successful_classifications": self.successful_classifications,
            "classification_success_rate": (
                self.successful_classifications / max(self.tickets_processed, 1)
            ),
            "collaboration_requests": self.collaboration_requests,
            "collaboration_rate": (
                self.collaboration_requests / max(self.tickets_processed, 1)
            ),
            "average_confidence": self.classification_confidence_threshold,
            "learning_enabled": self.learning_enabled
        }

        return {**base_metrics, **intake_metrics}
