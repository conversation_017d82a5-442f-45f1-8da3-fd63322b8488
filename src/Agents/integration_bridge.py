"""
Agentic AI Integration Bridge
============================

This module provides a bridge between the new Agentic AI agents and the existing
backend infrastructure. It maintains backward compatibility while enabling the
new autonomous agent capabilities.

Key Features:
- Seamless integration with existing backend endpoints
- Backward compatibility with old agent interfaces
- Autonomous workflow orchestration
- Legacy system integration
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union
import json
import uuid

# Import new Agentic AI agents
from .intake_agent_ai import IntakeAgentAI
from .assignment_agent_ai import AssignmentAgentAI
from .notification_agent_ai import NotificationAgentAI
from .monitoring_agent_ai import MonitoringAgentAI
from .escalation_agent_ai import EscalationAgentAI
from .agent_orchestrator import AgentOrchestrator
from .agent_communication import AgentCommunicationHub

logger = logging.getLogger(__name__)


class AgenticAIBridge:
    """
    Integration bridge that connects Agentic AI agents with existing backend systems.
    
    This bridge provides:
    - Backward compatibility with existing API endpoints
    - Autonomous agent workflow orchestration
    - Legacy system integration
    - Seamless transition from scripted to autonomous agents
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the Agentic AI Bridge.
        
        Args:
            config: Configuration for agents and integration
        """
        self.config = config or {}
        
        # Initialize communication hub
        self.communication_hub = AgentCommunicationHub()
        
        # Initialize agents with proper parameters
        # Note: Using mock/test configurations for now
        # Initialize data manager for reference data
        intake_config = self.config.get('intake_config', {})

        # Handle test mode
        if intake_config.get('test_mode'):
            # Create mock data manager for testing
            class MockDataManager:
                def __init__(self):
                    self.reference_data = {"test": "data"}
            data_manager = MockDataManager()
        else:
            try:
                from src.data.data_manager import DataManager
                data_manager = DataManager()
            except Exception as e:
                logger.warning(f"Could not initialize DataManager: {e}")
                data_manager = None

        self.intake_agent = IntakeAgentAI(
            db_connection=intake_config.get('db_connection'),
            data_manager=data_manager,
            agent_id="intake_agent_main"
        )

        self.assignment_agent = AssignmentAgentAI(
            db_connection=self.config.get('assignment_config', {}).get('db_connection'),
            agent_id="assignment_agent_main"
        )

        self.notification_agent = NotificationAgentAI(
            agent_id="notification_agent_main"
        )

        self.monitoring_agent = MonitoringAgentAI(
            agent_id="monitoring_agent_main"
        )

        self.escalation_agent = EscalationAgentAI(
            agent_id="escalation_agent_main"
        )
        
        # Initialize orchestrator
        self.orchestrator = AgentOrchestrator(
            communication_hub=self.communication_hub
        )
        
        # Register agents with orchestrator
        self._register_agents()
        
        # Legacy compatibility layer
        self.legacy_mode = self.config.get('legacy_mode', False)
        
        logger.info("Agentic AI Bridge initialized with autonomous agents")
    
    def _register_agents(self):
        """Register all agents with the orchestrator"""
        agents = [
            self.intake_agent,
            self.assignment_agent,
            self.notification_agent,
            self.monitoring_agent,
            self.escalation_agent
        ]
        
        for agent in agents:
            self.orchestrator.register_agent(agent)
            self.communication_hub.register_agent(agent)
    
    # ===== LEGACY COMPATIBILITY METHODS =====
    
    def process_new_ticket(self, ticket_name: str, ticket_description: str, 
                          ticket_title: str, due_date: str, 
                          priority_initial: str = "Medium", 
                          user_email: str = None) -> Dict[str, Any]:
        """
        Legacy-compatible ticket processing method.
        
        This method maintains the same interface as the old IntakeClassificationAgent
        while using the new Agentic AI workflow underneath.
        """
        try:
            # Convert legacy parameters to new format
            ticket_data = {
                "title": ticket_title,
                "description": ticket_description,
                "requester_name": ticket_name,
                "user_email": user_email,
                "due_date": due_date,
                "priority": priority_initial,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "ticket_number": f"TKT-{uuid.uuid4().hex[:8].upper()}",
                "source": "api"
            }
            
            # Process through autonomous workflow
            result = asyncio.run(self._process_ticket_autonomous(ticket_data))
            
            # Convert result to legacy format
            return self._convert_to_legacy_format(result, ticket_data)
            
        except Exception as e:
            logger.error(f"Error in legacy ticket processing: {e}")
            return {
                "success": False,
                "error": str(e),
                "ticket_number": ticket_data.get("ticket_number", "Unknown")
            }
    
    async def _process_ticket_autonomous(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process ticket through the autonomous Agentic AI workflow.
        
        This orchestrates the complete workflow:
        Intake → Classification → Assignment → Notification → Monitoring
        """
        workflow_id = f"ticket_workflow_{uuid.uuid4().hex[:8]}"
        
        # Define the autonomous workflow
        workflow_steps = [
            {
                "agent_id": self.intake_agent.agent_id,
                "action": "process_ticket_autonomous",
                "input_data": {"ticket_data": ticket_data}
            },
            {
                "agent_id": self.assignment_agent.agent_id,
                "action": "assign_ticket_autonomous",
                "input_data": {"ticket_data": ticket_data},
                "depends_on": [0]  # Depends on intake completion
            },
            {
                "agent_id": self.notification_agent.agent_id,
                "action": "send_notification_autonomous",
                "input_data": {
                    "notification_request": {
                        "type": "ticket_created",
                        "ticket_data": ticket_data
                    }
                },
                "depends_on": [1]  # Depends on assignment completion
            },
            {
                "agent_id": self.monitoring_agent.agent_id,
                "action": "monitor_system_autonomous",
                "input_data": {
                    "monitoring_request": {
                        "type": "ticket_monitoring",
                        "ticket_data": ticket_data
                    }
                },
                "depends_on": [2]  # Depends on notification completion
            }
        ]
        
        # Register and execute workflow
        self.orchestrator.register_workflow(workflow_id, workflow_steps)
        result = await self.orchestrator.execute_workflow(workflow_id)
        
        return result
    
    def _convert_to_legacy_format(self, agentic_result: Dict[str, Any], 
                                 original_ticket: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Agentic AI result to legacy format for backward compatibility"""
        
        # Extract results from workflow steps
        workflow_results = agentic_result.get("step_results", [])
        
        # Get intake result (step 0)
        intake_result = workflow_results[0] if len(workflow_results) > 0 else {}
        classification_data = intake_result.get("classification_result", {})
        
        # Get assignment result (step 1)
        assignment_result = workflow_results[1] if len(workflow_results) > 1 else {}
        assignment_data = assignment_result.get("assignment_result", {})
        
        # Get notification result (step 2)
        notification_result = workflow_results[2] if len(workflow_results) > 2 else {}
        
        # Build legacy-compatible response
        legacy_result = {
            "success": agentic_result.get("success", False),
            "ticket_number": original_ticket.get("ticket_number", "Unknown"),
            "new_ticket": {
                "ticket_number": original_ticket.get("ticket_number"),
                "title": original_ticket.get("title"),
                "description": original_ticket.get("description"),
                "requester_name": original_ticket.get("requester_name"),
                "user_email": original_ticket.get("user_email"),
                "due_date": original_ticket.get("due_date"),
                "date": datetime.now().strftime("%Y-%m-%d"),
                "time": datetime.now().strftime("%H:%M:%S"),
                "classified_data": classification_data,
                "assignment_result": {
                    "assigned_technician": assignment_data.get("assigned_technician", "Unassigned"),
                    "technician_email": assignment_data.get("technician_email", ""),
                    "assignment_reasoning": assignment_data.get("reasoning", ""),
                    "status": "Assigned" if assignment_data.get("success") else "Open"
                },
                "notification_result": {
                    "notifications_sent": notification_result.get("notifications_sent", 0),
                    "notification_success": notification_result.get("success", False)
                }
            }
        }
        
        return legacy_result
    
    # ===== NEW AGENTIC AI METHODS =====
    
    async def process_ticket_agentic(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process ticket using full Agentic AI capabilities.
        
        This method provides access to the complete autonomous workflow
        with all advanced features.
        """
        return await self._process_ticket_autonomous(ticket_data)
    
    async def escalate_ticket(self, ticket_data: Dict[str, Any], 
                            escalation_criteria: Dict[str, Any] = None) -> Dict[str, Any]:
        """Escalate ticket using autonomous escalation agent"""
        escalation_request = {
            "ticket_data": ticket_data,
            "escalation_criteria": escalation_criteria or {},
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        return await self.escalation_agent.escalate_ticket_autonomous(escalation_request)
    
    async def monitor_system_health(self) -> Dict[str, Any]:
        """Monitor system health using autonomous monitoring agent"""
        monitoring_request = {
            "type": "system_health",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        return await self.monitoring_agent.monitor_system_autonomous(monitoring_request)
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents"""
        return {
            "intake_agent": self.intake_agent.get_status(),
            "assignment_agent": self.assignment_agent.get_status(),
            "notification_agent": self.notification_agent.get_status(),
            "monitoring_agent": self.monitoring_agent.get_status(),
            "escalation_agent": self.escalation_agent.get_status(),
            "orchestrator": self.orchestrator.get_status(),
            "communication_hub": self.communication_hub.get_status()
        }
    
    def get_workflow_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent workflow execution history"""
        return self.orchestrator.get_workflow_history(limit)
    
    # ===== HEALTH CHECK METHODS =====
    
    def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check for the Agentic AI system"""
        try:
            agent_statuses = self.get_agent_status()
            
            # Check if all agents are healthy
            all_healthy = all(
                status.get("state") != "ERROR" 
                for status in agent_statuses.values()
            )
            
            return {
                "status": "healthy" if all_healthy else "degraded",
                "agents": agent_statuses,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "version": "1.0.0"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
