"""
Base Agent Class for Agentic AI Framework
==========================================

This module defines the foundation for all autonomous AI agents in the system.
Each agent inherits from BaseAgent and implements autonomous decision-making,
goal-oriented behavior, and collaborative capabilities.

Key Features:
- Autonomous decision-making with reasoning
- Goal-oriented planning and execution
- Context awareness and adaptation
- Inter-agent communication
- Learning from outcomes
- Error handling and recovery
"""

import uuid
import logging
import asyncio
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass, field
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AgentState(Enum):
    """Agent operational states"""
    IDLE = "idle"
    THINKING = "thinking"
    PLANNING = "planning"
    EXECUTING = "executing"
    COMMUNICATING = "communicating"
    LEARNING = "learning"
    ERROR = "error"
    PAUSED = "paused"


class AgentCapability(Enum):
    """Agent capabilities and skills"""
    TICKET_ANALYSIS = "ticket_analysis"
    CLASSIFICATION = "classification"
    ASSIGNMENT = "assignment"
    NOTIFICATION = "notification"
    MONITORING = "monitoring"
    ESCALATION = "escalation"
    LEARNING = "learning"
    COMMUNICATION = "communication"
    PLANNING = "planning"
    DECISION_MAKING = "decision_making"


@dataclass
class AgentGoal:
    """Represents an agent's goal or objective"""
    goal_id: str
    description: str
    priority: int  # 1-10, 10 being highest
    deadline: Optional[datetime] = None
    context: Dict[str, Any] = field(default_factory=dict)
    success_criteria: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    status: str = "active"  # active, completed, failed, paused


@dataclass
class AgentDecision:
    """Represents a decision made by an agent"""
    decision_id: str
    agent_id: str
    context: Dict[str, Any]
    reasoning: str
    action: str
    confidence: float  # 0.0 to 1.0
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    outcome: Optional[str] = None


@dataclass
class AgentMemory:
    """Agent's memory for learning and adaptation"""
    experiences: List[Dict[str, Any]] = field(default_factory=list)
    patterns: Dict[str, Any] = field(default_factory=dict)
    preferences: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)


class BaseAgent(ABC):
    """
    Base class for all autonomous AI agents in the system.
    
    Provides core functionality for:
    - Autonomous decision-making
    - Goal-oriented behavior
    - Context awareness
    - Inter-agent communication
    - Learning and adaptation
    """
    
    def __init__(self, agent_id: str, name: str, capabilities: List[AgentCapability]):
        """
        Initialize the base agent.
        
        Args:
            agent_id: Unique identifier for the agent
            name: Human-readable name for the agent
            capabilities: List of agent capabilities
        """
        self.agent_id = agent_id
        self.name = name
        self.capabilities = capabilities
        self.state = AgentState.IDLE
        self.goals: List[AgentGoal] = []
        self.memory = AgentMemory()
        self.communication_hub = None
        self.created_at = datetime.now(timezone.utc)
        self.last_activity = datetime.now(timezone.utc)
        
        # Performance tracking
        self.decisions_made = 0
        self.successful_actions = 0
        self.failed_actions = 0
        
        logger.info(f"Agent {self.name} ({self.agent_id}) initialized with capabilities: {[c.value for c in capabilities]}")
    
    def set_communication_hub(self, hub):
        """Set the communication hub for inter-agent communication"""
        self.communication_hub = hub
        logger.info(f"Agent {self.name} connected to communication hub")
    
    def add_goal(self, description: str, priority: int, context: Dict[str, Any] = None, 
                 success_criteria: List[str] = None, deadline: datetime = None) -> str:
        """
        Add a new goal for the agent to pursue.
        
        Args:
            description: Goal description
            priority: Priority level (1-10)
            context: Additional context for the goal
            success_criteria: List of success criteria
            deadline: Optional deadline
            
        Returns:
            Goal ID
        """
        goal_id = str(uuid.uuid4())
        goal = AgentGoal(
            goal_id=goal_id,
            description=description,
            priority=priority,
            context=context or {},
            success_criteria=success_criteria or [],
            deadline=deadline
        )
        self.goals.append(goal)
        self.goals.sort(key=lambda g: g.priority, reverse=True)
        
        logger.info(f"Agent {self.name} added goal: {description} (Priority: {priority})")
        return goal_id
    
    def get_active_goals(self) -> List[AgentGoal]:
        """Get all active goals sorted by priority"""
        return [g for g in self.goals if g.status == "active"]
    
    def complete_goal(self, goal_id: str, outcome: str = "completed"):
        """Mark a goal as completed"""
        for goal in self.goals:
            if goal.goal_id == goal_id:
                goal.status = outcome
                logger.info(f"Agent {self.name} completed goal: {goal.description}")
                break
    
    @abstractmethod
    async def think(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Core thinking process - analyze situation and determine next actions.
        
        Args:
            context: Current context and available information
            
        Returns:
            Thought process results and potential actions
        """
        pass
    
    @abstractmethod
    async def plan(self, goal: AgentGoal, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Create a plan to achieve a specific goal.
        
        Args:
            goal: The goal to plan for
            context: Current context
            
        Returns:
            List of planned actions
        """
        pass
    
    @abstractmethod
    async def execute_action(self, action: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a specific action.
        
        Args:
            action: Action to execute
            context: Current context
            
        Returns:
            Execution results
        """
        pass
    
    async def make_decision(self, context: Dict[str, Any], options: List[Dict[str, Any]]) -> AgentDecision:
        """
        Make an autonomous decision based on context and available options.
        
        Args:
            context: Current context
            options: Available options to choose from
            
        Returns:
            Decision made by the agent
        """
        self.state = AgentState.THINKING
        
        # Analyze context and options
        analysis = await self.analyze_situation(context, options)
        
        # Generate reasoning
        reasoning = await self.generate_reasoning(analysis, options)
        
        # Select best option
        selected_option = await self.select_best_option(options, analysis, reasoning)
        
        # Calculate confidence
        confidence = await self.calculate_confidence(selected_option, analysis)
        
        decision = AgentDecision(
            decision_id=str(uuid.uuid4()),
            agent_id=self.agent_id,
            context=context,
            reasoning=reasoning,
            action=selected_option.get('action', 'unknown'),
            confidence=confidence
        )
        
        self.decisions_made += 1
        self.last_activity = datetime.now(timezone.utc)
        
        logger.info(f"Agent {self.name} made decision: {decision.action} (Confidence: {confidence:.2f})")
        
        return decision
    
    async def analyze_situation(self, context: Dict[str, Any], options: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze the current situation and context"""
        # Default implementation - can be overridden by specific agents
        return {
            "context_summary": context,
            "available_options": len(options),
            "agent_state": self.state.value,
            "active_goals": len(self.get_active_goals())
        }
    
    async def generate_reasoning(self, analysis: Dict[str, Any], options: List[Dict[str, Any]]) -> str:
        """Generate reasoning for decision-making"""
        # Default implementation - can be overridden by specific agents
        return f"Based on analysis of {len(options)} options and current context, selecting optimal action."
    
    async def select_best_option(self, options: List[Dict[str, Any]], analysis: Dict[str, Any], reasoning: str) -> Dict[str, Any]:
        """Select the best option from available choices"""
        # Default implementation - select first option
        # Should be overridden by specific agents with domain-specific logic
        return options[0] if options else {"action": "no_action"}
    
    async def calculate_confidence(self, selected_option: Dict[str, Any], analysis: Dict[str, Any]) -> float:
        """Calculate confidence in the selected option"""
        # Default implementation - can be overridden by specific agents
        return 0.8  # Default confidence level
    
    async def learn_from_outcome(self, decision: AgentDecision, outcome: Dict[str, Any]):
        """Learn from the outcome of a decision/action"""
        self.state = AgentState.LEARNING
        
        # Store experience
        experience = {
            "decision_id": decision.decision_id,
            "context": decision.context,
            "action": decision.action,
            "outcome": outcome,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "success": outcome.get("success", False)
        }
        
        self.memory.experiences.append(experience)
        
        # Update performance metrics
        if outcome.get("success", False):
            self.successful_actions += 1
        else:
            self.failed_actions += 1
        
        # Update performance metrics in memory
        total_actions = self.successful_actions + self.failed_actions
        if total_actions > 0:
            self.memory.performance_metrics["success_rate"] = self.successful_actions / total_actions
            self.memory.performance_metrics["total_decisions"] = self.decisions_made
            self.memory.performance_metrics["total_actions"] = total_actions
        
        logger.info(f"Agent {self.name} learned from outcome: {outcome.get('success', 'unknown')}")
        
        self.state = AgentState.IDLE
    
    async def communicate(self, recipient_id: str, message_type: str, content: Dict[str, Any]):
        """Send a message to another agent"""
        if self.communication_hub:
            await self.communication_hub.send_message(
                sender_id=self.agent_id,
                recipient_id=recipient_id,
                message_type=message_type,
                content=content
            )
        else:
            logger.warning(f"Agent {self.name} attempted to communicate but no communication hub is set")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "state": self.state.value,
            "capabilities": [c.value for c in self.capabilities],
            "active_goals": len(self.get_active_goals()),
            "decisions_made": self.decisions_made,
            "success_rate": self.memory.performance_metrics.get("success_rate", 0.0),
            "last_activity": self.last_activity.isoformat(),
            "uptime": (datetime.now(timezone.utc) - self.created_at).total_seconds()
        }
