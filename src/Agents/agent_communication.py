"""
Agent Communication Hub for Agentic AI Framework
================================================

This module provides inter-agent communication capabilities, allowing autonomous
agents to collaborate, share information, and coordinate their actions.

Key Features:
- Message routing between agents
- Broadcast capabilities
- Message queuing and delivery
- Communication protocols
- Event-driven messaging
- Message persistence and logging
"""

import uuid
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Types of messages that can be sent between agents"""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    COLLABORATION = "collaboration"
    STATUS_UPDATE = "status_update"
    GOAL_SHARING = "goal_sharing"
    RESOURCE_REQUEST = "resource_request"
    ESCALATION = "escalation"
    BROADCAST = "broadcast"


class MessagePriority(Enum):
    """Message priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


@dataclass
class AgentMessage:
    """Represents a message between agents"""
    message_id: str
    sender_id: str
    recipient_id: str  # Can be "ALL" for broadcast
    message_type: MessageType
    priority: MessagePriority
    content: Dict[str, Any]
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    requires_response: bool = False
    response_timeout: Optional[int] = None  # seconds
    correlation_id: Optional[str] = None  # For request-response pairs
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentSubscription:
    """Represents an agent's subscription to message types"""
    agent_id: str
    message_types: List[MessageType]
    callback: Callable
    filters: Dict[str, Any] = field(default_factory=dict)


class AgentCommunicationHub:
    """
    Central hub for managing communication between autonomous agents.
    
    Provides:
    - Message routing and delivery
    - Subscription management
    - Broadcast capabilities
    - Message queuing
    - Communication logging
    """
    
    def __init__(self):
        """Initialize the communication hub"""
        self.agents: Dict[str, Any] = {}  # agent_id -> agent instance
        self.subscriptions: Dict[str, List[AgentSubscription]] = defaultdict(list)
        self.message_queue: Dict[str, deque] = defaultdict(deque)  # agent_id -> message queue
        self.message_history: List[AgentMessage] = []
        self.pending_responses: Dict[str, AgentMessage] = {}  # correlation_id -> original message
        self.running = False
        
        logger.info("Agent Communication Hub initialized")
    
    def register_agent(self, agent):
        """Register an agent with the communication hub"""
        self.agents[agent.agent_id] = agent
        agent.set_communication_hub(self)
        logger.info(f"Agent {agent.name} ({agent.agent_id}) registered with communication hub")
    
    def unregister_agent(self, agent_id: str):
        """Unregister an agent from the communication hub"""
        if agent_id in self.agents:
            agent_name = self.agents[agent_id].name
            del self.agents[agent_id]
            # Clean up subscriptions
            self.subscriptions[agent_id] = []
            # Clear message queue
            self.message_queue[agent_id].clear()
            logger.info(f"Agent {agent_name} ({agent_id}) unregistered from communication hub")
    
    def subscribe(self, agent_id: str, message_types: List[MessageType], 
                  callback: Callable, filters: Optional[Dict[str, Any]] = None):
        """
        Subscribe an agent to specific message types.
        
        Args:
            agent_id: ID of the subscribing agent
            message_types: List of message types to subscribe to
            callback: Function to call when matching messages arrive
            filters: Optional filters for message content
        """
        subscription = AgentSubscription(
            agent_id=agent_id,
            message_types=message_types,
            callback=callback,
            filters=filters or {}
        )
        self.subscriptions[agent_id].append(subscription)
        
        logger.info(f"Agent {agent_id} subscribed to message types: {[mt.value for mt in message_types]}")
    
    async def send_message(self, sender_id: str, recipient_id: str, message_type: str,
                          content: Dict[str, Any], priority: str = "NORMAL",
                          requires_response: bool = False, response_timeout: Optional[int] = None) -> str:
        """
        Send a message from one agent to another.
        
        Args:
            sender_id: ID of the sending agent
            recipient_id: ID of the receiving agent (or "ALL" for broadcast)
            message_type: Type of message
            content: Message content
            priority: Message priority
            requires_response: Whether a response is required
            response_timeout: Timeout for response in seconds
            
        Returns:
            Message ID
        """
        message_id = str(uuid.uuid4())
        correlation_id = str(uuid.uuid4()) if requires_response else None
        
        message = AgentMessage(
            message_id=message_id,
            sender_id=sender_id,
            recipient_id=recipient_id,
            message_type=MessageType(message_type),
            priority=MessagePriority[priority.upper()],
            content=content,
            requires_response=requires_response,
            response_timeout=response_timeout,
            correlation_id=correlation_id
        )
        
        # Store message in history
        self.message_history.append(message)
        
        # If response is required, store for tracking
        if requires_response and correlation_id:
            self.pending_responses[correlation_id] = message
        
        # Route message
        await self._route_message(message)
        
        logger.info(f"Message sent from {sender_id} to {recipient_id}: {message_type}")
        return message_id
    
    async def _route_message(self, message: AgentMessage):
        """Route a message to its intended recipient(s)"""
        if message.recipient_id == "ALL":
            # Broadcast message
            await self._broadcast_message(message)
        else:
            # Direct message
            await self._deliver_message(message)
    
    async def _broadcast_message(self, message: AgentMessage):
        """Broadcast a message to all subscribed agents"""
        for agent_id, subscriptions in self.subscriptions.items():
            if agent_id != message.sender_id:  # Don't send to sender
                for subscription in subscriptions:
                    if message.message_type in subscription.message_types:
                        if self._message_matches_filters(message, subscription.filters):
                            await self._queue_message(agent_id, message)
    
    async def _deliver_message(self, message: AgentMessage):
        """Deliver a message to a specific agent"""
        if message.recipient_id in self.agents:
            await self._queue_message(message.recipient_id, message)
        else:
            logger.warning(f"Attempted to deliver message to unknown agent: {message.recipient_id}")
    
    async def _queue_message(self, agent_id: str, message: AgentMessage):
        """Queue a message for an agent"""
        self.message_queue[agent_id].append(message)
        
        # Notify agent if it has subscriptions
        for subscription in self.subscriptions[agent_id]:
            if message.message_type in subscription.message_types:
                if self._message_matches_filters(message, subscription.filters):
                    try:
                        await subscription.callback(message)
                    except Exception as e:
                        logger.error(f"Error in agent {agent_id} message callback: {e}")
    
    def _message_matches_filters(self, message: AgentMessage, filters: Dict[str, Any]) -> bool:
        """Check if a message matches the subscription filters"""
        if not filters:
            return True
        
        for key, value in filters.items():
            if key in message.content:
                if message.content[key] != value:
                    return False
            else:
                return False
        
        return True
    
    async def send_response(self, agent_id: str, original_message: AgentMessage, 
                           response_content: Dict[str, Any]):
        """Send a response to a message that required a response"""
        if original_message.correlation_id:
            await self.send_message(
                sender_id=agent_id,
                recipient_id=original_message.sender_id,
                message_type="response",
                content={
                    "original_message_id": original_message.message_id,
                    "response": response_content
                },
                priority="NORMAL"
            )
            
            # Remove from pending responses
            if original_message.correlation_id in self.pending_responses:
                del self.pending_responses[original_message.correlation_id]
    
    def get_messages_for_agent(self, agent_id: str, limit: int = 10) -> List[AgentMessage]:
        """Get recent messages for an agent"""
        messages = list(self.message_queue[agent_id])
        return messages[-limit:] if len(messages) > limit else messages
    
    def get_message_history(self, agent_id: Optional[str] = None, limit: int = 50) -> List[AgentMessage]:
        """Get message history, optionally filtered by agent"""
        if agent_id:
            filtered_messages = [
                msg for msg in self.message_history 
                if msg.sender_id == agent_id or msg.recipient_id == agent_id
            ]
            return filtered_messages[-limit:] if len(filtered_messages) > limit else filtered_messages
        else:
            return self.message_history[-limit:] if len(self.message_history) > limit else self.message_history
    
    def get_communication_stats(self) -> Dict[str, Any]:
        """Get communication statistics"""
        total_messages = len(self.message_history)
        messages_by_type = defaultdict(int)
        messages_by_agent = defaultdict(int)
        
        for message in self.message_history:
            messages_by_type[message.message_type.value] += 1
            messages_by_agent[message.sender_id] += 1
        
        return {
            "total_messages": total_messages,
            "registered_agents": len(self.agents),
            "active_subscriptions": sum(len(subs) for subs in self.subscriptions.values()),
            "pending_responses": len(self.pending_responses),
            "messages_by_type": dict(messages_by_type),
            "messages_by_agent": dict(messages_by_agent),
            "queue_sizes": {agent_id: len(queue) for agent_id, queue in self.message_queue.items()}
        }
    
    async def start(self):
        """Start the communication hub"""
        self.running = True
        logger.info("Agent Communication Hub started")
        
        # Start background tasks for message processing
        asyncio.create_task(self._process_pending_responses())
    
    async def stop(self):
        """Stop the communication hub"""
        self.running = False
        logger.info("Agent Communication Hub stopped")
    
    async def _process_pending_responses(self):
        """Background task to handle response timeouts"""
        while self.running:
            current_time = datetime.now(timezone.utc)
            expired_responses = []
            
            for correlation_id, message in self.pending_responses.items():
                if message.response_timeout:
                    elapsed = (current_time - message.timestamp).total_seconds()
                    if elapsed > message.response_timeout:
                        expired_responses.append(correlation_id)
            
            # Handle expired responses
            for correlation_id in expired_responses:
                message = self.pending_responses[correlation_id]
                logger.warning(f"Response timeout for message {message.message_id} from {message.sender_id}")
                del self.pending_responses[correlation_id]
            
            await asyncio.sleep(5)  # Check every 5 seconds
